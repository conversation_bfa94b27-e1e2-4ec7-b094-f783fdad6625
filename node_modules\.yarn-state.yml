# Warning: This file is automatically generated. Removing it is fine, but will
# cause your node_modules installation to become invalidated.

__metadata:
  version: 1
  nmMode: classic

"@cspotcode/source-map-support@npm:0.8.1":
  locations:
    - "node_modules/@cspotcode/source-map-support"

"@img/sharp-win32-x64@npm:0.33.5":
  locations:
    - "node_modules/@img/sharp-win32-x64"

"@isaacs/balanced-match@npm:4.0.1":
  locations:
    - "node_modules/@isaacs/balanced-match"

"@isaacs/brace-expansion@npm:5.0.0":
  locations:
    - "node_modules/@isaacs/brace-expansion"

"@isaacs/cliui@npm:8.0.2":
  locations:
    - "node_modules/@isaacs/cliui"

"@jridgewell/resolve-uri@npm:3.1.2":
  locations:
    - "node_modules/@jridgewell/resolve-uri"

"@jridgewell/sourcemap-codec@npm:1.5.4":
  locations:
    - "node_modules/@jridgewell/sourcemap-codec"

"@jridgewell/trace-mapping@npm:0.3.9":
  locations:
    - "node_modules/@jridgewell/trace-mapping"

"@tsconfig/node10@npm:1.0.11":
  locations:
    - "node_modules/@tsconfig/node10"

"@tsconfig/node12@npm:1.0.11":
  locations:
    - "node_modules/@tsconfig/node12"

"@tsconfig/node14@npm:1.0.3":
  locations:
    - "node_modules/@tsconfig/node14"

"@tsconfig/node16@npm:1.0.4":
  locations:
    - "node_modules/@tsconfig/node16"

"@types/axios@npm:0.14.4":
  locations:
    - "node_modules/@types/axios"

"@types/body-parser@npm:1.19.6":
  locations:
    - "node_modules/@types/body-parser"

"@types/connect@npm:3.4.38":
  locations:
    - "node_modules/@types/connect"

"@types/cors@npm:2.8.19":
  locations:
    - "node_modules/@types/cors"

"@types/express-serve-static-core@npm:5.0.7":
  locations:
    - "node_modules/@types/express-serve-static-core"

"@types/express@npm:5.0.3":
  locations:
    - "node_modules/@types/express"

"@types/http-errors@npm:2.0.5":
  locations:
    - "node_modules/@types/http-errors"

"@types/mime@npm:1.3.5":
  locations:
    - "node_modules/@types/mime"

"@types/morgan@npm:1.9.10":
  locations:
    - "node_modules/@types/morgan"

"@types/node@npm:24.0.15":
  locations:
    - "node_modules/@types/node"

"@types/qs@npm:6.14.0":
  locations:
    - "node_modules/@types/qs"

"@types/range-parser@npm:1.2.7":
  locations:
    - "node_modules/@types/range-parser"

"@types/send@npm:0.17.5":
  locations:
    - "node_modules/@types/send"

"@types/serve-static@npm:1.15.8":
  locations:
    - "node_modules/@types/serve-static"

"accepts@npm:2.0.0":
  locations:
    - "node_modules/accepts"

"acorn-walk@npm:8.3.4":
  locations:
    - "node_modules/acorn-walk"

"acorn@npm:8.15.0":
  locations:
    - "node_modules/acorn"

"ansi-regex@npm:5.0.1":
  locations:
    - "node_modules/ansi-regex"

"ansi-regex@npm:6.1.0":
  locations:
    - "node_modules/strip-ansi/node_modules/ansi-regex"

"ansi-styles@npm:4.3.0":
  locations:
    - "node_modules/ansi-styles"

"ansi-styles@npm:6.2.1":
  locations:
    - "node_modules/wrap-ansi/node_modules/ansi-styles"

"anymatch@npm:3.1.3":
  locations:
    - "node_modules/anymatch"

"arg@npm:4.1.3":
  locations:
    - "node_modules/arg"

"asynckit@npm:0.4.0":
  locations:
    - "node_modules/asynckit"

"axios@npm:1.10.0":
  locations:
    - "node_modules/axios"

"balanced-match@npm:1.0.2":
  locations:
    - "node_modules/balanced-match"

"basic-auth@npm:2.0.1":
  locations:
    - "node_modules/basic-auth"

"binary-extensions@npm:2.3.0":
  locations:
    - "node_modules/binary-extensions"

"body-parser@npm:2.2.0":
  locations:
    - "node_modules/body-parser"

"brace-expansion@npm:1.1.12":
  locations:
    - "node_modules/brace-expansion"

"braces@npm:3.0.3":
  locations:
    - "node_modules/braces"

"bytes@npm:3.1.2":
  locations:
    - "node_modules/bytes"

"call-bind-apply-helpers@npm:1.0.2":
  locations:
    - "node_modules/call-bind-apply-helpers"

"call-bound@npm:1.0.4":
  locations:
    - "node_modules/call-bound"

"chatbot-zalo@workspace:.":
  locations:
    - ""
  bin:
    ".":
      "nodemon": "nodemon/bin/nodemon.js"
      "prettier": "prettier/bin/prettier.cjs"
      "rimraf": "rimraf/dist/esm/bin.mjs"
      "tsc": "typescript/bin/tsc"
      "tsserver": "typescript/bin/tsserver"
      "ts-node": "ts-node/dist/bin.js"
      "ts-node-cwd": "ts-node/dist/bin-cwd.js"
      "ts-node-esm": "ts-node/dist/bin-esm.js"
      "ts-node-script": "ts-node/dist/bin-script.js"
      "ts-node-transpile-only": "ts-node/dist/bin-transpile.js"
      "ts-script": "ts-node/dist/bin-script-deprecated.js"
      "semver": "semver/bin/semver.js"
      "nodetouch": "touch/bin/nodetouch.js"
      "glob": "glob/dist/esm/bin.mjs"
      "acorn": "acorn/bin/acorn"
      "tldts": "tldts/bin/cli.js"
      "node-which": "which/bin/node-which"

"chokidar@npm:3.6.0":
  locations:
    - "node_modules/chokidar"

"color-convert@npm:2.0.1":
  locations:
    - "node_modules/color-convert"

"color-name@npm:1.1.4":
  locations:
    - "node_modules/color-name"

"color-string@npm:1.9.1":
  locations:
    - "node_modules/color-string"

"color@npm:4.2.3":
  locations:
    - "node_modules/color"

"combined-stream@npm:1.0.8":
  locations:
    - "node_modules/combined-stream"

"concat-map@npm:0.0.1":
  locations:
    - "node_modules/concat-map"

"content-disposition@npm:1.0.0":
  locations:
    - "node_modules/content-disposition"

"content-type@npm:1.0.5":
  locations:
    - "node_modules/content-type"

"cookie-signature@npm:1.2.2":
  locations:
    - "node_modules/cookie-signature"

"cookie@npm:0.7.2":
  locations:
    - "node_modules/cookie"

"cors@npm:2.8.5":
  locations:
    - "node_modules/cors"

"create-require@npm:1.1.1":
  locations:
    - "node_modules/create-require"

"cross-spawn@npm:7.0.6":
  locations:
    - "node_modules/cross-spawn"

"crypto-js@npm:4.2.0":
  locations:
    - "node_modules/crypto-js"

"debug@virtual:247862abc0d56a498737cd31bd1091cc5432e519635453393e51e4f2cf465e7c4b8b69bcfc53455cc9a1fbf431bee98638a5cdc3ada415b133064a695e46d175#npm:2.6.9":
  locations:
    - "node_modules/morgan/node_modules/debug"

"debug@virtual:a345fb10767f7507bb836583037bd0538e45be4c0b7d01b378c28de2575623867b926ab06543de0e9efc893338034a56314289b227582d36e15650769aac23d1#npm:4.4.1":
  locations:
    - "node_modules/debug"
  aliases:
    - "virtual:b3d2aaf918b7353bd0443ec66626f23848085fec7626c8198deeeb11823698c64a4c04de542c97d383c7d367525cd5f3dbbc5626864e9aab284c22611d9f418b#npm:4.4.1"

"delayed-stream@npm:1.0.0":
  locations:
    - "node_modules/delayed-stream"

"depd@npm:2.0.0":
  locations:
    - "node_modules/depd"

"detect-libc@npm:2.0.4":
  locations:
    - "node_modules/detect-libc"

"diff@npm:4.0.2":
  locations:
    - "node_modules/diff"

"dotenv@npm:17.2.0":
  locations:
    - "node_modules/dotenv"

"dunder-proto@npm:1.0.1":
  locations:
    - "node_modules/dunder-proto"

"eastasianwidth@npm:0.2.0":
  locations:
    - "node_modules/eastasianwidth"

"ee-first@npm:1.1.1":
  locations:
    - "node_modules/ee-first"

"emoji-regex@npm:8.0.0":
  locations:
    - "node_modules/emoji-regex"

"emoji-regex@npm:9.2.2":
  locations:
    - "node_modules/string-width/node_modules/emoji-regex"

"encodeurl@npm:2.0.0":
  locations:
    - "node_modules/encodeurl"

"es-define-property@npm:1.0.1":
  locations:
    - "node_modules/es-define-property"

"es-errors@npm:1.3.0":
  locations:
    - "node_modules/es-errors"

"es-object-atoms@npm:1.1.1":
  locations:
    - "node_modules/es-object-atoms"

"es-set-tostringtag@npm:2.1.0":
  locations:
    - "node_modules/es-set-tostringtag"

"escape-html@npm:1.0.3":
  locations:
    - "node_modules/escape-html"

"etag@npm:1.8.1":
  locations:
    - "node_modules/etag"

"express@npm:5.1.0":
  locations:
    - "node_modules/express"

"fill-range@npm:7.1.1":
  locations:
    - "node_modules/fill-range"

"finalhandler@npm:2.1.0":
  locations:
    - "node_modules/finalhandler"

"follow-redirects@virtual:0c877f1ea627088aa7a1bdfef2b23b74690ff0132006b80a74eda5cd1c7ddf9090e5571b1d9e31fbfe0c1a2d6e4bb2fbebb5c63ff72b896b6cbc80384f357768#npm:1.15.9":
  locations:
    - "node_modules/follow-redirects"

"foreground-child@npm:3.3.1":
  locations:
    - "node_modules/foreground-child"

"form-data@npm:4.0.4":
  locations:
    - "node_modules/form-data"

"forwarded@npm:0.2.0":
  locations:
    - "node_modules/forwarded"

"fresh@npm:2.0.0":
  locations:
    - "node_modules/fresh"

"function-bind@npm:1.1.2":
  locations:
    - "node_modules/function-bind"

"get-intrinsic@npm:1.3.0":
  locations:
    - "node_modules/get-intrinsic"

"get-proto@npm:1.0.1":
  locations:
    - "node_modules/get-proto"

"glob-parent@npm:5.1.2":
  locations:
    - "node_modules/glob-parent"

"glob@npm:11.0.3":
  locations:
    - "node_modules/glob"

"gopd@npm:1.2.0":
  locations:
    - "node_modules/gopd"

"has-flag@npm:3.0.0":
  locations:
    - "node_modules/has-flag"

"has-symbols@npm:1.1.0":
  locations:
    - "node_modules/has-symbols"

"has-tostringtag@npm:1.0.2":
  locations:
    - "node_modules/has-tostringtag"

"hasown@npm:2.0.2":
  locations:
    - "node_modules/hasown"

"helmet@npm:8.1.0":
  locations:
    - "node_modules/helmet"

"http-errors@npm:2.0.0":
  locations:
    - "node_modules/http-errors"

"iconv-lite@npm:0.6.3":
  locations:
    - "node_modules/iconv-lite"

"ignore-by-default@npm:1.0.1":
  locations:
    - "node_modules/ignore-by-default"

"inherits@npm:2.0.4":
  locations:
    - "node_modules/inherits"

"ipaddr.js@npm:1.9.1":
  locations:
    - "node_modules/ipaddr.js"

"is-arrayish@npm:0.3.2":
  locations:
    - "node_modules/is-arrayish"

"is-binary-path@npm:2.1.0":
  locations:
    - "node_modules/is-binary-path"

"is-extglob@npm:2.1.1":
  locations:
    - "node_modules/is-extglob"

"is-fullwidth-code-point@npm:3.0.0":
  locations:
    - "node_modules/is-fullwidth-code-point"

"is-glob@npm:4.0.3":
  locations:
    - "node_modules/is-glob"

"is-number@npm:7.0.0":
  locations:
    - "node_modules/is-number"

"is-promise@npm:4.0.0":
  locations:
    - "node_modules/is-promise"

"isexe@npm:2.0.0":
  locations:
    - "node_modules/isexe"

"jackspeak@npm:4.1.1":
  locations:
    - "node_modules/jackspeak"

"lru-cache@npm:11.1.0":
  locations:
    - "node_modules/lru-cache"

"make-error@npm:1.3.6":
  locations:
    - "node_modules/make-error"

"math-intrinsics@npm:1.1.0":
  locations:
    - "node_modules/math-intrinsics"

"media-typer@npm:1.1.0":
  locations:
    - "node_modules/media-typer"

"merge-descriptors@npm:2.0.0":
  locations:
    - "node_modules/merge-descriptors"

"mime-db@npm:1.52.0":
  locations:
    - "node_modules/mime-db"

"mime-db@npm:1.54.0":
  locations:
    - "node_modules/mime-types/node_modules/mime-db"

"mime-types@npm:2.1.35":
  locations:
    - "node_modules/form-data/node_modules/mime-types"

"mime-types@npm:3.0.1":
  locations:
    - "node_modules/mime-types"

"minimatch@npm:10.0.3":
  locations:
    - "node_modules/glob/node_modules/minimatch"

"minimatch@npm:3.1.2":
  locations:
    - "node_modules/minimatch"

"minipass@npm:7.1.2":
  locations:
    - "node_modules/minipass"

"morgan@npm:1.10.1":
  locations:
    - "node_modules/morgan"

"ms@npm:2.0.0":
  locations:
    - "node_modules/morgan/node_modules/ms"

"ms@npm:2.1.3":
  locations:
    - "node_modules/ms"

"negotiator@npm:1.0.0":
  locations:
    - "node_modules/negotiator"

"nodemon@npm:3.1.10":
  locations:
    - "node_modules/nodemon"

"normalize-path@npm:3.0.0":
  locations:
    - "node_modules/normalize-path"

"object-assign@npm:4.1.1":
  locations:
    - "node_modules/object-assign"

"object-inspect@npm:1.13.4":
  locations:
    - "node_modules/object-inspect"

"on-finished@npm:2.3.0":
  locations:
    - "node_modules/morgan/node_modules/on-finished"

"on-finished@npm:2.4.1":
  locations:
    - "node_modules/on-finished"

"on-headers@npm:1.1.0":
  locations:
    - "node_modules/on-headers"

"once@npm:1.4.0":
  locations:
    - "node_modules/once"

"package-json-from-dist@npm:1.0.1":
  locations:
    - "node_modules/package-json-from-dist"

"pako@npm:2.1.0":
  locations:
    - "node_modules/pako"

"parseurl@npm:1.3.3":
  locations:
    - "node_modules/parseurl"

"path-key@npm:3.1.1":
  locations:
    - "node_modules/path-key"

"path-scurry@npm:2.0.0":
  locations:
    - "node_modules/path-scurry"

"path-to-regexp@npm:8.2.0":
  locations:
    - "node_modules/path-to-regexp"

"picomatch@npm:2.3.1":
  locations:
    - "node_modules/picomatch"

"prettier@npm:3.6.2":
  locations:
    - "node_modules/prettier"

"proxy-addr@npm:2.0.7":
  locations:
    - "node_modules/proxy-addr"

"proxy-from-env@npm:1.1.0":
  locations:
    - "node_modules/proxy-from-env"

"pstree.remy@npm:1.1.8":
  locations:
    - "node_modules/pstree.remy"

"qs@npm:6.14.0":
  locations:
    - "node_modules/qs"

"range-parser@npm:1.2.1":
  locations:
    - "node_modules/range-parser"

"raw-body@npm:3.0.0":
  locations:
    - "node_modules/raw-body"

"readdirp@npm:3.6.0":
  locations:
    - "node_modules/readdirp"

"rimraf@npm:6.0.1":
  locations:
    - "node_modules/rimraf"

"router@npm:2.2.0":
  locations:
    - "node_modules/router"

"safe-buffer@npm:5.1.2":
  locations:
    - "node_modules/basic-auth/node_modules/safe-buffer"

"safe-buffer@npm:5.2.1":
  locations:
    - "node_modules/safe-buffer"

"safer-buffer@npm:2.1.2":
  locations:
    - "node_modules/safer-buffer"

"semver@npm:7.7.2":
  locations:
    - "node_modules/semver"

"send@npm:1.2.0":
  locations:
    - "node_modules/send"

"serve-static@npm:2.2.0":
  locations:
    - "node_modules/serve-static"

"setprototypeof@npm:1.2.0":
  locations:
    - "node_modules/setprototypeof"

"sharp@npm:0.33.5":
  locations:
    - "node_modules/sharp"

"shebang-command@npm:2.0.0":
  locations:
    - "node_modules/shebang-command"

"shebang-regex@npm:3.0.0":
  locations:
    - "node_modules/shebang-regex"

"side-channel-list@npm:1.0.0":
  locations:
    - "node_modules/side-channel-list"

"side-channel-map@npm:1.0.1":
  locations:
    - "node_modules/side-channel-map"

"side-channel-weakmap@npm:1.0.2":
  locations:
    - "node_modules/side-channel-weakmap"

"side-channel@npm:1.1.0":
  locations:
    - "node_modules/side-channel"

"signal-exit@npm:4.1.0":
  locations:
    - "node_modules/signal-exit"

"simple-swizzle@npm:0.2.2":
  locations:
    - "node_modules/simple-swizzle"

"simple-update-notifier@npm:2.0.0":
  locations:
    - "node_modules/simple-update-notifier"

"spark-md5@npm:3.0.2":
  locations:
    - "node_modules/spark-md5"

"statuses@npm:2.0.1":
  locations:
    - "node_modules/http-errors/node_modules/statuses"

"statuses@npm:2.0.2":
  locations:
    - "node_modules/statuses"

"string-width@npm:4.2.3":
  locations:
    - "node_modules/wrap-ansi-cjs/node_modules/string-width"
    - "node_modules/string-width-cjs"

"string-width@npm:5.1.2":
  locations:
    - "node_modules/string-width"

"strip-ansi@npm:6.0.1":
  locations:
    - "node_modules/wrap-ansi-cjs/node_modules/strip-ansi"
    - "node_modules/strip-ansi-cjs"
    - "node_modules/string-width-cjs/node_modules/strip-ansi"

"strip-ansi@npm:7.1.0":
  locations:
    - "node_modules/strip-ansi"

"supports-color@npm:5.5.0":
  locations:
    - "node_modules/supports-color"

"tldts-core@npm:6.1.86":
  locations:
    - "node_modules/tldts-core"

"tldts@npm:6.1.86":
  locations:
    - "node_modules/tldts"

"to-regex-range@npm:5.0.1":
  locations:
    - "node_modules/to-regex-range"

"toidentifier@npm:1.0.1":
  locations:
    - "node_modules/toidentifier"

"touch@npm:3.1.1":
  locations:
    - "node_modules/touch"

"tough-cookie@npm:5.1.2":
  locations:
    - "node_modules/tough-cookie"

"ts-node@virtual:ee0a7a0ede7a5ae96673bea0bfd8acca12973f46c0a6cc5aa9f3750a126db3f602e661403c2abe41c3d4e297c27c838c9ced0748eba2c7e17d6793cfb2801375#npm:10.9.2":
  locations:
    - "node_modules/ts-node"

"type-is@npm:2.0.1":
  locations:
    - "node_modules/type-is"

"typescript@patch:typescript@npm%3A5.8.3#optional!builtin<compat/typescript>::version=5.8.3&hash=5786d5":
  locations:
    - "node_modules/typescript"

"undefsafe@npm:2.0.5":
  locations:
    - "node_modules/undefsafe"

"undici-types@npm:7.8.0":
  locations:
    - "node_modules/undici-types"

"unpipe@npm:1.0.0":
  locations:
    - "node_modules/unpipe"

"v8-compile-cache-lib@npm:3.0.1":
  locations:
    - "node_modules/v8-compile-cache-lib"

"vary@npm:1.1.2":
  locations:
    - "node_modules/vary"

"which@npm:2.0.2":
  locations:
    - "node_modules/which"

"wrap-ansi@npm:7.0.0":
  locations:
    - "node_modules/wrap-ansi-cjs"

"wrap-ansi@npm:8.1.0":
  locations:
    - "node_modules/wrap-ansi"

"wrappy@npm:1.0.2":
  locations:
    - "node_modules/wrappy"

"ws@virtual:aaeeea562c73c6642c217272685c2d79317c4515e808fd5e293f21b663aa7cfda3b99a35d3b4667b7c51f51f439044babe44ea212e82c5938a3201f3b8d029e2#npm:8.18.3":
  locations:
    - "node_modules/ws"

"yn@npm:3.1.1":
  locations:
    - "node_modules/yn"

"zca-js@npm:2.0.0-beta.25":
  locations:
    - "node_modules/zca-js"
