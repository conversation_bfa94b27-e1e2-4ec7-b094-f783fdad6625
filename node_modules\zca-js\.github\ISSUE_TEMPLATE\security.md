---
name: Security Vulnerability Report
about: Report a security vulnerability
title: "[SECURITY]"
labels: ["SECURITY", "security", "high-priority"]
assignees: ""
---

## Security Vulnerability Report

**IMPORTANT**: This issue will be handled privately and with the highest priority. Please do not disclose details publicly until the issue is resolved.

### Vulnerability Type
<!-- Please select the type of vulnerability -->
- [ ] Authentication bypass
- [ ] Data exposure/information disclosure
- [ ] Code injection
- [ ] Cross-site scripting (XSS)
- [ ] Cross-site request forgery (CSRF)
- [ ] Denial of service (DoS)
- [ ] Privilege escalation
- [ ] Dependency vulnerability
- [ ] Other (please describe)

### Severity Level
<!-- Please assess the severity -->
- [ ] Critical (immediate action required)
- [ ] High (action required within 24 hours)
- [ ] Medium (action required within 1 week)
- [ ] Low (action required within 1 month)

### Description
<!-- Please provide a clear and detailed description of the vulnerability -->

### Steps to Reproduce
<!-- Please provide clear steps to reproduce the vulnerability -->
1. 
2. 
3. 

### Expected Behavior
<!-- What should happen normally? -->

### Actual Behavior
<!-- What actually happens due to the vulnerability? -->

### Environment
<!-- Please provide details about the environment -->
- **zca-js Version**: 
- **Node.js Version**: 
- **Operating System**: 
- **Browser** (if applicable): 

### Impact Assessment
<!-- What is the potential impact of this vulnerability? -->
- **Data at Risk**: 
- **Users Affected**: 
- **Business Impact**: 

### Suggested Fix
<!-- Do you have any suggestions for fixing this vulnerability? (optional) -->

### Additional Information
<!-- Any additional context, logs, or evidence -->
<!-- You can drag and drop files here -->

### Disclosure Timeline
<!-- When do you plan to disclose this publicly? (if applicable) -->

---

**Note**: This report will be reviewed by the security team and handled according to our [Security Policy](https://github.com/RFS-ADRENO/zca-js/blob/main/SECURITY.md). We appreciate your responsible disclosure and will work to resolve this issue promptly.

**⚠️ Warning**: This is an unofficial API library. Please ensure your testing does not violate Zalo's terms of service or put any accounts at risk. 