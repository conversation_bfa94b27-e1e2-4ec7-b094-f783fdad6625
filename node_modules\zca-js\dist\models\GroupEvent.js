export var GroupEventType;
(function (GroupEventType) {
    GroupEventType[GroupEventType["JOIN_REQUEST"] = 0] = "JOIN_REQUEST";
    GroupEventType[GroupEventType["JOIN"] = 1] = "JOIN";
    GroupEventType[GroupEventType["LEAVE"] = 2] = "LEAVE";
    GroupEventType[GroupEventType["REMOVE_MEMBER"] = 3] = "REMOVE_MEMBER";
    GroupEventType[GroupEventType["BLOCK_MEMBER"] = 4] = "BLOCK_MEMBER";
    GroupEventType[GroupEventType["UPDATE_SETTING"] = 5] = "UPDATE_SETTING";
    GroupEventType[GroupEventType["UPDATE"] = 6] = "UPDATE";
    GroupEventType[GroupEventType["NEW_LINK"] = 7] = "NEW_LINK";
    GroupEventType[GroupEventType["ADD_ADMIN"] = 8] = "ADD_ADMIN";
    GroupEventType[GroupEventType["REMOVE_ADMIN"] = 9] = "REMOVE_ADMIN";
    GroupEventType[GroupEventType["NEW_PIN_TOPIC"] = 10] = "NEW_PIN_TOPIC";
    GroupEventType[GroupEventType["UPDATE_PIN_TOPIC"] = 11] = "UPDATE_PIN_TOPIC";
    GroupEventType[GroupEventType["REORDER_PIN_TOPIC"] = 12] = "REORDER_PIN_TOPIC";
    GroupEventType[GroupEventType["UPDATE_BOARD"] = 13] = "UPDATE_BOARD";
    GroupEventType[GroupEventType["REMOVE_BOARD"] = 14] = "REMOVE_BOARD";
    GroupEventType[GroupEventType["UPDATE_TOPIC"] = 15] = "UPDATE_TOPIC";
    GroupEventType[GroupEventType["UNPIN_TOPIC"] = 16] = "UNPIN_TOPIC";
    GroupEventType[GroupEventType["REMOVE_TOPIC"] = 17] = "REMOVE_TOPIC";
    GroupEventType[GroupEventType["ACCEPT_REMIND"] = 18] = "ACCEPT_REMIND";
    GroupEventType[GroupEventType["REJECT_REMIND"] = 19] = "REJECT_REMIND";
    GroupEventType[GroupEventType["REMIND_TOPIC"] = 20] = "REMIND_TOPIC";
    GroupEventType[GroupEventType["UPDATE_AVATAR"] = 21] = "UPDATE_AVATAR";
    GroupEventType[GroupEventType["UNKNOWN"] = 22] = "UNKNOWN";
})(GroupEventType || (GroupEventType = {}));
export function initializeGroupEvent(uid, data, type) {
    var _a;
    const threadId = "group_id" in data ? data.group_id : data.groupId;
    if (type == GroupEventType.JOIN_REQUEST) {
        return { type, data: data, threadId, isSelf: false };
    }
    else if (type == GroupEventType.NEW_PIN_TOPIC ||
        type == GroupEventType.UNPIN_TOPIC ||
        type == GroupEventType.UPDATE_PIN_TOPIC) {
        return {
            type,
            data: data,
            threadId,
            isSelf: data.actorId == uid,
        };
    }
    else if (type == GroupEventType.REORDER_PIN_TOPIC) {
        return {
            type,
            data: data,
            threadId,
            isSelf: data.actorId == uid,
        };
    }
    else if (type == GroupEventType.UPDATE_BOARD || type == GroupEventType.REMOVE_BOARD) {
        return {
            type,
            data: data,
            threadId,
            isSelf: data.sourceId == uid,
        };
    }
    else if (type == GroupEventType.ACCEPT_REMIND || type == GroupEventType.REJECT_REMIND) {
        return {
            type,
            data: data,
            threadId,
            isSelf: data.updateMembers.some((memberId) => memberId == uid),
        };
    }
    else if (type == GroupEventType.REMIND_TOPIC) {
        return {
            type,
            data: data,
            threadId,
            isSelf: data.creatorId == uid,
        };
    }
    else {
        const baseData = data;
        return {
            type,
            data: baseData,
            threadId,
            isSelf: ((_a = baseData.updateMembers) === null || _a === void 0 ? void 0 : _a.some((member) => member.id == uid)) || baseData.sourceId == uid,
        };
    }
}
