{"version": 3, "file": "ChatwootService.js", "sourceRoot": "", "sources": ["../../src/services/ChatwootService.ts"], "names": [], "mappings": ";;;;;;AAAA,kDAA6C;AAC7C,4CAAyC;AA0DzC,MAAa,eAAe;IAClB,MAAM,CAAiB;IACvB,SAAS,CAAgB;IAEjC,YAAY,MAAsB;QAChC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,SAAS,GAAG,eAAK,CAAC,MAAM,CAAC;YAC5B,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO;YAC5B,OAAO,EAAE;gBACP,kBAAkB,EAAE,IAAI,CAAC,MAAM,CAAC,cAAc;gBAC9C,cAAc,EAAE,kBAAkB;aACnC;YACD,OAAO,EAAE,KAAK;SACf,CAAC,CAAC;IACL,CAAC;IAKD,KAAK,CAAC,uBAAuB,CAAC,UAAkB;QAC9C,IAAI,CAAC;YACH,eAAM,CAAC,IAAI,CAAC,gCAAgC,EAAE,EAAE,UAAU,EAAE,CAAC,CAAC;YAE9D,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,oBAAoB,IAAI,CAAC,MAAM,CAAC,SAAS,kBAAkB,EAAE;gBACrG,MAAM,EAAE;oBACN,CAAC,EAAE,UAAU;iBACd;aACF,CAAC,CAAC;YAEH,MAAM,QAAQ,GAAG,QAAQ,CAAC,IAAI,CAAC,OAAO,IAAI,EAAE,CAAC;YAG7C,MAAM,eAAe,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAY,EAAE,EAAE,CACrD,OAAO,CAAC,qBAAqB,EAAE,QAAQ,KAAK,UAAU,CACvD,CAAC;YAEF,IAAI,eAAe,EAAE,CAAC;gBACpB,eAAM,CAAC,IAAI,CAAC,wBAAwB,EAAE;oBACpC,SAAS,EAAE,eAAe,CAAC,EAAE;oBAC7B,IAAI,EAAE,eAAe,CAAC,IAAI;iBAC3B,CAAC,CAAC;gBACH,OAAO,eAAe,CAAC;YACzB,CAAC;YAED,eAAM,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;YACzC,OAAO,IAAI,CAAC;QAEd,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,eAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE;gBACvC,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,MAAM,EAAE,KAAK,CAAC,QAAQ,EAAE,MAAM;gBAC9B,UAAU,EAAE,KAAK,CAAC,QAAQ,EAAE,UAAU;gBACtC,YAAY,EAAE,KAAK,CAAC,QAAQ,EAAE,IAAI;gBAClC,UAAU;aACX,CAAC,CAAC;YACH,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,mBAAmB,CAAC,WAKzB;QACC,IAAI,CAAC;YACH,eAAM,CAAC,IAAI,CAAC,sCAAsC,EAAE;gBAClD,IAAI,EAAE,WAAW,CAAC,IAAI;gBACtB,UAAU,EAAE,WAAW,CAAC,UAAU;aACnC,CAAC,CAAC;YAGH,IAAI,OAAO,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;YAEzE,IAAI,CAAC,OAAO,EAAE,CAAC;gBAEb,MAAM,OAAO,GAAG;oBACd,IAAI,EAAE,WAAW,CAAC,IAAI;oBACtB,YAAY,EAAE,WAAW,CAAC,YAAY;oBACtC,KAAK,EAAE,WAAW,CAAC,KAAK;oBACxB,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO;oBAC7B,qBAAqB,EAAE;wBACrB,QAAQ,EAAE,WAAW,CAAC,UAAU;wBAChC,MAAM,EAAE,cAAc;qBACvB;iBACF,CAAC;gBAEF,eAAM,CAAC,IAAI,CAAC,mCAAmC,EAAE;oBAC/C,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;iBAC1C,CAAC,CAAC;gBAEH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,oBAAoB,IAAI,CAAC,MAAM,CAAC,SAAS,WAAW,EAAE,OAAO,CAAC,CAAC;gBAG1G,MAAM,YAAY,GAAG,QAAQ,CAAC,IAAI,CAAC;gBACnC,OAAO,GAAG,YAAY,CAAC,OAAO,EAAE,OAAO,IAAI,YAAY,CAAC,OAAO,IAAI,YAAY,CAAC;gBAEhF,eAAM,CAAC,IAAI,CAAC,kCAAkC,EAAE;oBAC9C,SAAS,EAAE,OAAO,EAAE,EAAE;oBACtB,cAAc,EAAE,OAAO,EAAE,eAAe,EAAE,MAAM;oBAChD,iBAAiB,EAAE,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC;oBAC5C,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;iBAC9C,CAAC,CAAC;gBAGH,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE,CAAC;oBAC5B,eAAM,CAAC,KAAK,CAAC,sDAAsD,EAAE;wBACnE,YAAY,EAAE,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,IAAI,EAAE,CAAC,CAAC;qBACpD,CAAC,CAAC;oBACH,MAAM,IAAI,KAAK,CAAC,kDAAkD,CAAC,CAAC;gBACtE,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,eAAM,CAAC,IAAI,CAAC,wBAAwB,EAAE;oBACpC,SAAS,EAAE,OAAO,CAAC,EAAE;oBACrB,cAAc,EAAE,OAAO,CAAC,eAAe,EAAE,MAAM;iBAChD,CAAC,CAAC;YACL,CAAC;YAGD,MAAM,YAAY,GAAG,OAAO,CAAC,eAAe,EAAE,IAAI,CAChD,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,KAAK,IAAI,CAAC,MAAM,CAAC,OAAO,CAC1C,CAAC;YAEF,IAAI,CAAC,YAAY,EAAE,CAAC;gBAClB,eAAM,CAAC,IAAI,CAAC,uCAAuC,EAAE;oBACnD,SAAS,EAAE,OAAO,CAAC,EAAE;oBACrB,eAAe,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO;oBACpC,gBAAgB,EAAE,OAAO,CAAC,eAAe,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC;iBAClE,CAAC,CAAC;gBAGH,IAAI,CAAC;oBACH,MAAM,mBAAmB,GAAG;wBAC1B,UAAU,EAAE,OAAO,CAAC,EAAE;wBACtB,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO;wBAC7B,SAAS,EAAE,QAAQ,WAAW,CAAC,UAAU,IAAI,IAAI,CAAC,GAAG,EAAE,EAAE;qBAC1D,CAAC;oBAEF,eAAM,CAAC,IAAI,CAAC,qCAAqC,EAAE;wBACjD,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,mBAAmB,EAAE,IAAI,EAAE,CAAC,CAAC;qBACtD,CAAC,CAAC;oBAEH,MAAM,oBAAoB,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CACpD,oBAAoB,IAAI,CAAC,MAAM,CAAC,SAAS,kBAAkB,EAC3D,mBAAmB,CACpB,CAAC;oBAEF,MAAM,eAAe,GAAG,oBAAoB,CAAC,IAAI,CAAC;oBAElD,eAAM,CAAC,IAAI,CAAC,oCAAoC,EAAE;wBAChD,cAAc,EAAE,eAAe,CAAC,EAAE;wBAClC,QAAQ,EAAE,eAAe,CAAC,SAAS;qBACpC,CAAC,CAAC;oBAEH,OAAO;wBACL,OAAO;wBACP,QAAQ,EAAE,eAAe,CAAC,SAAS;qBACpC,CAAC;gBAEJ,CAAC;gBAAC,OAAO,iBAAsB,EAAE,CAAC;oBAChC,eAAM,CAAC,KAAK,CAAC,wCAAwC,EAAE;wBACrD,KAAK,EAAE,iBAAiB,CAAC,OAAO;wBAChC,MAAM,EAAE,iBAAiB,CAAC,QAAQ,EAAE,MAAM;wBAC1C,UAAU,EAAE,iBAAiB,CAAC,QAAQ,EAAE,UAAU;wBAClD,YAAY,EAAE,iBAAiB,CAAC,QAAQ,EAAE,IAAI;wBAC9C,SAAS,EAAE,OAAO,CAAC,EAAE;wBACrB,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO;qBAC7B,CAAC,CAAC;oBAGH,MAAM,gBAAgB,GAAG,QAAQ,WAAW,CAAC,UAAU,IAAI,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC;oBAExE,eAAM,CAAC,IAAI,CAAC,0BAA0B,EAAE;wBACtC,QAAQ,EAAE,gBAAgB;wBAC1B,SAAS,EAAE,OAAO,CAAC,EAAE;qBACtB,CAAC,CAAC;oBAEH,OAAO;wBACL,OAAO;wBACP,QAAQ,EAAE,gBAAgB;qBAC3B,CAAC;gBACJ,CAAC;YACH,CAAC;YAED,OAAO;gBACL,OAAO;gBACP,QAAQ,EAAE,YAAY,CAAC,SAAS;aACjC,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,eAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE;gBAC5C,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,MAAM,EAAE,KAAK,CAAC,QAAQ,EAAE,MAAM;gBAC9B,UAAU,EAAE,KAAK,CAAC,QAAQ,EAAE,UAAU;gBACtC,YAAY,EAAE,KAAK,CAAC,QAAQ,EAAE,IAAI;gBAClC,WAAW,EAAE;oBACX,IAAI,EAAE,WAAW,CAAC,IAAI;oBACtB,UAAU,EAAE,WAAW,CAAC,UAAU;oBAClC,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO;iBAC7B;aACF,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,kBAAkB,CAAC,QAAgB;QACvC,IAAI,CAAC;YACH,eAAM,CAAC,IAAI,CAAC,mCAAmC,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAC;YAE/D,MAAM,OAAO,GAAG;gBACd,SAAS,EAAE,QAAQ;gBACnB,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO;aAC9B,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,oBAAoB,IAAI,CAAC,MAAM,CAAC,SAAS,gBAAgB,EAAE,OAAO,CAAC,CAAC;YAC/G,MAAM,YAAY,GAAyB,QAAQ,CAAC,IAAI,CAAC;YAEzD,eAAM,CAAC,IAAI,CAAC,mCAAmC,EAAE;gBAC/C,cAAc,EAAE,YAAY,CAAC,EAAE;aAChC,CAAC,CAAC;YAEH,OAAO,YAAY,CAAC;QAEtB,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,eAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE;gBAC5C,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,MAAM,EAAE,KAAK,CAAC,QAAQ,EAAE,MAAM;gBAC9B,UAAU,EAAE,KAAK,CAAC,QAAQ,EAAE,UAAU;gBACtC,YAAY,EAAE,KAAK,CAAC,QAAQ,EAAE,IAAI;gBAClC,WAAW,EAAE;oBACX,QAAQ;oBACR,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO;iBAC7B;aACF,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,aAAa,CACjB,cAAsB,EACtB,OAAe,EACf,cAAuC,UAAU;QAEjD,IAAI,CAAC;YACH,eAAM,CAAC,IAAI,CAAC,8BAA8B,EAAE;gBAC1C,cAAc;gBACd,WAAW;gBACX,aAAa,EAAE,OAAO,CAAC,MAAM;aAC9B,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG;gBACd,OAAO,EAAE,OAAO;gBAChB,YAAY,EAAE,WAAW;gBACzB,OAAO,EAAE,KAAK;aACf,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CACxC,oBAAoB,IAAI,CAAC,MAAM,CAAC,SAAS,kBAAkB,cAAc,WAAW,EACpF,OAAO,CACR,CAAC;YACF,MAAM,OAAO,GAAoB,QAAQ,CAAC,IAAI,CAAC;YAE/C,eAAM,CAAC,IAAI,CAAC,8BAA8B,EAAE;gBAC1C,SAAS,EAAE,OAAO,CAAC,EAAE;gBACrB,cAAc,EAAE,OAAO,CAAC,eAAe;aACxC,CAAC,CAAC;YAEH,OAAO,OAAO,CAAC;QAEjB,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,eAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE;gBACvC,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,MAAM,EAAE,KAAK,CAAC,QAAQ,EAAE,MAAM;gBAC9B,UAAU,EAAE,KAAK,CAAC,QAAQ,EAAE,UAAU;gBACtC,YAAY,EAAE,KAAK,CAAC,QAAQ,EAAE,IAAI;gBAClC,WAAW,EAAE;oBACX,cAAc;oBACd,WAAW;oBACX,aAAa,EAAE,OAAO,CAAC,MAAM;iBAC9B;aACF,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,wBAAwB,CAAC,SAAiB;QAC9C,IAAI,CAAC;YACH,eAAM,CAAC,IAAI,CAAC,gCAAgC,EAAE,EAAE,SAAS,EAAE,CAAC,CAAC;YAE7D,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,oBAAoB,IAAI,CAAC,MAAM,CAAC,SAAS,gBAAgB,EAAE;gBACnG,MAAM,EAAE;oBACN,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO;oBAC7B,MAAM,EAAE,MAAM;iBACf;aACF,CAAC,CAAC;YAEH,MAAM,aAAa,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC;YAG/C,MAAM,oBAAoB,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC,IAAS,EAAE,EAAE,CAC5D,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,EAAE,KAAK,SAAS,CACpC,CAAC;YAEF,IAAI,oBAAoB,EAAE,CAAC;gBACzB,eAAM,CAAC,IAAI,CAAC,6BAA6B,EAAE;oBACzC,cAAc,EAAE,oBAAoB,CAAC,EAAE;iBACxC,CAAC,CAAC;gBACH,OAAO,oBAAoB,CAAC;YAC9B,CAAC;YAED,eAAM,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;YAC9C,OAAO,IAAI,CAAC;QAEd,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,eAAM,CAAC,KAAK,CAAC,sCAAsC,EAAE;gBACnD,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,MAAM,EAAE,KAAK,CAAC,QAAQ,EAAE,MAAM;gBAC9B,UAAU,EAAE,KAAK,CAAC,QAAQ,EAAE,UAAU;gBACtC,YAAY,EAAE,KAAK,CAAC,QAAQ,EAAE,IAAI;gBAClC,WAAW,EAAE;oBACX,SAAS;oBACT,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO;iBAC7B;aACF,CAAC,CAAC;YACH,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,kBAAkB,CAAC,WAMxB;QAKC,IAAI,CAAC;YACH,eAAM,CAAC,IAAI,CAAC,sCAAsC,EAAE;gBAClD,IAAI,EAAE,WAAW,CAAC,IAAI;gBACtB,MAAM,EAAE,WAAW,CAAC,MAAM;gBAC1B,SAAS,EAAE,WAAW,CAAC,SAAS;aACjC,CAAC,CAAC;YAGH,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC;gBAC3D,IAAI,EAAE,WAAW,CAAC,IAAI;gBACtB,UAAU,EAAE,WAAW,CAAC,MAAM;gBAC9B,YAAY,EAAE,SAAS;gBACvB,KAAK,EAAE,SAAS;aACjB,CAAC,CAAC;YAGH,IAAI,YAAY,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,EAAG,CAAC,CAAC;YAEpE,IAAI,CAAC,YAAY,EAAE,CAAC;gBAClB,YAAY,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;YACzD,CAAC;YAGD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,aAAa,CACtC,YAAY,CAAC,EAAE,EACf,WAAW,CAAC,OAAO,EACnB,UAAU,CACX,CAAC;YAEF,eAAM,CAAC,IAAI,CAAC,qCAAqC,EAAE;gBACjD,SAAS,EAAE,OAAO,CAAC,EAAE;gBACrB,cAAc,EAAE,YAAY,CAAC,EAAE;gBAC/B,SAAS,EAAE,OAAO,CAAC,EAAE;aACtB,CAAC,CAAC;YAEH,OAAO;gBACL,OAAO;gBACP,YAAY;gBACZ,OAAO;aACR,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,eAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE;gBAC7C,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,MAAM,EAAE,KAAK,CAAC,QAAQ,EAAE,MAAM;gBAC9B,UAAU,EAAE,KAAK,CAAC,QAAQ,EAAE,UAAU;gBACtC,YAAY,EAAE,KAAK,CAAC,QAAQ,EAAE,IAAI;gBAClC,WAAW,EAAE;oBACX,IAAI,EAAE,WAAW,CAAC,IAAI;oBACtB,MAAM,EAAE,WAAW,CAAC,MAAM;oBAC1B,SAAS,EAAE,WAAW,CAAC,SAAS;oBAChC,aAAa,EAAE,WAAW,CAAC,OAAO,CAAC,MAAM;iBAC1C;aACF,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,gBAAgB,CACpB,cAAsB,EACtB,OAAe;QAEf,OAAO,IAAI,CAAC,aAAa,CAAC,cAAc,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;IACjE,CAAC;IAKD,KAAK,CAAC,cAAc;QAClB,IAAI,CAAC;YACH,eAAM,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;YAE3C,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;YAE7D,eAAM,CAAC,IAAI,CAAC,gCAAgC,EAAE;gBAC5C,OAAO,EAAE,QAAQ,CAAC,IAAI;aACvB,CAAC,CAAC;YAEH,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,eAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE;gBACzC,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,MAAM,EAAE,KAAK,CAAC,QAAQ,EAAE,MAAM;gBAC9B,UAAU,EAAE,KAAK,CAAC,QAAQ,EAAE,UAAU;gBACtC,YAAY,EAAE,KAAK,CAAC,QAAQ,EAAE,IAAI;gBAClC,MAAM,EAAE;oBACN,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO;oBAC5B,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc;oBACtC,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS;iBACjC;aACF,CAAC,CAAC;YACH,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;CACF;AAncD,0CAmcC"}