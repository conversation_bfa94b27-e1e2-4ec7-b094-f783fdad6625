import { Listener } from "./apis/listen.js";
import { type ContextSession, type Options, type ZPWServiceMap } from "./context.js";
import toughCookie from "tough-cookie";
import { acceptFriendRequestFactory } from "./apis/acceptFriendRequest.js";
import { addGroupDeputyFactory } from "./apis/addGroupDeputy.js";
import { addQuickMessageFactory } from "./apis/addQuickMessage.js";
import { addReactionFactory } from "./apis/addReaction.js";
import { addUnreadMarkFactory } from "./apis/addUnreadMark.js";
import { addUserToGroupFactory } from "./apis/addUserToGroup.js";
import { blockUserFactory } from "./apis/blockUser.js";
import { blockViewFeedFactory } from "./apis/blockViewFeed.js";
import { changeAccountAvatarFactory } from "./apis/changeAccountAvatar.js";
import { changeFriendAliasFactory } from "./apis/changeFriendAlias.js";
import { changeGroupAvatarFactory } from "./apis/changeGroupAvatar.js";
import { changeGroupNameFactory } from "./apis/changeGroupName.js";
import { changeGroupOwnerFactory } from "./apis/changeGroupOwner.js";
import { createGroupFactory } from "./apis/createGroup.js";
import { createNoteGroupFactory } from "./apis/createNoteGroup.js";
import { createPollFactory } from "./apis/createPoll.js";
import { createReminderFactory } from "./apis/createReminder.js";
import { deleteAvatarFactory } from "./apis/deleteAvatar.js";
import { deleteChatFactory } from "./apis/deleteChat.js";
import { deleteMessageFactory } from "./apis/deleteMessage.js";
import { disableGroupLinkFactory } from "./apis/disableGroupLink.js";
import { disperseGroupFactory } from "./apis/disperseGroup.js";
import { editNoteGroupFactory } from "./apis/editNoteGroup.js";
import { editReminderFactory } from "./apis/editReminder.js";
import { enableGroupLinkFactory } from "./apis/enableGroupLink.js";
import { fetchAccountInfoFactory } from "./apis/fetchAccountInfo.js";
import { findUserFactory } from "./apis/findUser.js";
import { forwardMessageFactory } from "./apis/forwardMessage.js";
import { getAliasListFactory } from "./apis/getAliasList.js";
import { getAllFriendsFactory } from "./apis/getAllFriends.js";
import { getAllGroupsFactory } from "./apis/getAllGroups.js";
import { getArchivedChatListFactory } from "./apis/getArchivedChatList.js";
import { getAutoDeleteChatFactory } from "./apis/getAutoDeleteChat.js";
import { getAvatarListFactory } from "./apis/getAvatarList.js";
import { getBizAccountFactory } from "./apis/getBizAccount.js";
import { getContextFactory } from "./apis/getContext.js";
import { getCookieFactory } from "./apis/getCookie.js";
import { getFriendBoardListFactory } from "./apis/getFriendBoardList.js";
import { getGroupInfoFactory } from "./apis/getGroupInfo.js";
import { getGroupLinkInfoFactory } from "./apis/getGroupLinkInfo.js";
import { getGroupMembersInfoFactory } from "./apis/getGroupMembersInfo.js";
import { getHiddenConversationsFactory } from "./apis/getHiddenConversations.js";
import { getLabelsFactory } from "./apis/getLabels.js";
import { getListBoardFactory } from "./apis/getListBoard.js";
import { getListReminderFactory } from "./apis/getListReminder.js";
import { getMuteFactory } from "./apis/getMute.js";
import { getOwnIdFactory } from "./apis/getOwnId.js";
import { getPinConversationsFactory } from "./apis/getPinConversations.js";
import { getPollDetailFactory } from "./apis/getPollDetail.js";
import { getQRFactory } from "./apis/getQR.js";
import { getQuickMessageFactory } from "./apis/getQuickMessage.js";
import { getReceivedFriendRequestsFactory } from "./apis/getReceivedFriendRequests.js";
import { getReminderFactory } from "./apis/getReminder.js";
import { getReminderResponsesFactory } from "./apis/getReminderResponses.js";
import { getSentFriendRequestFactory } from "./apis/getSentFriendRequest.js";
import { getStickersFactory } from "./apis/getStickers.js";
import { getStickersDetailFactory } from "./apis/getStickersDetail.js";
import { getUnreadMarkFactory } from "./apis/getUnreadMark.js";
import { getUserInfoFactory } from "./apis/getUserInfo.js";
import { inviteUserToGroupsFactory } from "./apis/inviteUserToGroups.js";
import { joinGroupFactory } from "./apis/joinGroup.js";
import { keepAliveFactory } from "./apis/keepAlive.js";
import { lastOnlineFactory } from "./apis/lastOnline.js";
import { leaveGroupFactory } from "./apis/leaveGroup.js";
import { lockPollFactory } from "./apis/lockPoll.js";
import { type LoginQRCallback } from "./apis/loginQR.js";
import { parseLinkFactory } from "./apis/parseLink.js";
import { removeFriendFactory } from "./apis/removeFriend.js";
import { removeFriendAliasFactory } from "./apis/removeFriendAlias.js";
import { removeGroupDeputyFactory } from "./apis/removeGroupDeputy.js";
import { removeQuickMessageFactory } from "./apis/removeQuickMessage.js";
import { removeReminderFactory } from "./apis/removeReminder.js";
import { removeUnreadMarkFactory } from "./apis/removeUnreadMark.js";
import { removeUserFromGroupFactory } from "./apis/removeUserFromGroup.js";
import { resetHiddenConversPinFactory } from "./apis/resetHiddenConversPin.js";
import { reuseAvatarFactory } from "./apis/reuseAvatar.js";
import { sendCardFactory } from "./apis/sendCard.js";
import { sendDeliveredEventFactory } from "./apis/sendDeliveredEvent.js";
import { sendFriendRequestFactory } from "./apis/sendFriendRequest.js";
import { sendLinkFactory } from "./apis/sendLink.js";
import { sendMessageFactory } from "./apis/sendMessage.js";
import { sendReportFactory } from "./apis/sendReport.js";
import { sendSeenEventFactory } from "./apis/sendSeenEvent.js";
import { sendStickerFactory } from "./apis/sendSticker.js";
import { sendTypingEventFactory } from "./apis/sendTypingEvent.js";
import { sendVideoFactory } from "./apis/sendVideo.js";
import { sendVoiceFactory } from "./apis/sendVoice.js";
import { setHiddenConversationsFactory } from "./apis/setHiddenConversations.js";
import { setMuteFactory } from "./apis/setMute.js";
import { setPinnedConversationsFactory } from "./apis/setPinnedConversations.js";
import { unblockUserFactory } from "./apis/unblockUser.js";
import { undoFactory } from "./apis/undo.js";
import { undoFriendRequestFactory } from "./apis/undoFriendRequest.js";
import { updateAutoDeleteChatFactory } from "./apis/updateAutoDeleteChat.js";
import { updateGroupSettingsFactory } from "./apis/updateGroupSettings.js";
import { updateHiddenConversPinFactory } from "./apis/updateHiddenConversPin.js";
import { updateLabelsFactory } from "./apis/updateLabels.js";
import { updateLangFactory } from "./apis/updateLang.js";
import { updateProfileFactory } from "./apis/updateProfile.js";
import { updateQuickMessageFactory } from "./apis/updateQuickMessage.js";
import { updateSettingsFactory } from "./apis/updateSettings.js";
import { uploadAttachmentFactory } from "./apis/uploadAttachment.js";
import { customFactory } from "./apis/custom.js";
export type Cookie = {
    domain: string;
    expirationDate: number;
    hostOnly: boolean;
    httpOnly: boolean;
    name: string;
    path: string;
    sameSite: string;
    secure: boolean;
    session: boolean;
    storeId: string;
    value: string;
};
export type Credentials = {
    imei: string;
    cookie: Cookie[] | toughCookie.SerializedCookie[] | {
        url: string;
        cookies: Cookie[];
    };
    userAgent: string;
    language?: string;
};
export declare class Zalo {
    private options;
    private enableEncryptParam;
    constructor(options?: Partial<Options>);
    private parseCookies;
    private validateParams;
    login(credentials: Credentials): Promise<API>;
    private loginCookie;
    loginQR(options?: {
        userAgent?: string;
        language?: string;
        qrPath?: string;
    }, callback?: LoginQRCallback): Promise<API>;
}
export declare class API {
    zpwServiceMap: ZPWServiceMap;
    listener: Listener;
    acceptFriendRequest: ReturnType<typeof acceptFriendRequestFactory>;
    addGroupDeputy: ReturnType<typeof addGroupDeputyFactory>;
    addQuickMessage: ReturnType<typeof addQuickMessageFactory>;
    addReaction: ReturnType<typeof addReactionFactory>;
    addUnreadMark: ReturnType<typeof addUnreadMarkFactory>;
    addUserToGroup: ReturnType<typeof addUserToGroupFactory>;
    blockUser: ReturnType<typeof blockUserFactory>;
    blockViewFeed: ReturnType<typeof blockViewFeedFactory>;
    changeAccountAvatar: ReturnType<typeof changeAccountAvatarFactory>;
    changeFriendAlias: ReturnType<typeof changeFriendAliasFactory>;
    changeGroupAvatar: ReturnType<typeof changeGroupAvatarFactory>;
    changeGroupName: ReturnType<typeof changeGroupNameFactory>;
    changeGroupOwner: ReturnType<typeof changeGroupOwnerFactory>;
    createGroup: ReturnType<typeof createGroupFactory>;
    createNoteGroup: ReturnType<typeof createNoteGroupFactory>;
    createPoll: ReturnType<typeof createPollFactory>;
    createReminder: ReturnType<typeof createReminderFactory>;
    deleteAvatarList: ReturnType<typeof deleteAvatarFactory>;
    deleteChat: ReturnType<typeof deleteChatFactory>;
    deleteMessage: ReturnType<typeof deleteMessageFactory>;
    disableGroupLink: ReturnType<typeof disableGroupLinkFactory>;
    disperseGroup: ReturnType<typeof disperseGroupFactory>;
    editNoteGroup: ReturnType<typeof editNoteGroupFactory>;
    editReminder: ReturnType<typeof editReminderFactory>;
    enableGroupLink: ReturnType<typeof enableGroupLinkFactory>;
    fetchAccountInfo: ReturnType<typeof fetchAccountInfoFactory>;
    findUser: ReturnType<typeof findUserFactory>;
    forwardMessage: ReturnType<typeof forwardMessageFactory>;
    getAliasList: ReturnType<typeof getAliasListFactory>;
    getAllFriends: ReturnType<typeof getAllFriendsFactory>;
    getAllGroups: ReturnType<typeof getAllGroupsFactory>;
    getArchivedChatList: ReturnType<typeof getArchivedChatListFactory>;
    getAutoDeleteChat: ReturnType<typeof getAutoDeleteChatFactory>;
    getAvatarList: ReturnType<typeof getAvatarListFactory>;
    getBizAccount: ReturnType<typeof getBizAccountFactory>;
    getContext: ReturnType<typeof getContextFactory>;
    getCookie: ReturnType<typeof getCookieFactory>;
    getFriendBoardList: ReturnType<typeof getFriendBoardListFactory>;
    getGroupInfo: ReturnType<typeof getGroupInfoFactory>;
    getGroupLinkInfo: ReturnType<typeof getGroupLinkInfoFactory>;
    getGroupMembersInfo: ReturnType<typeof getGroupMembersInfoFactory>;
    getHiddenConversations: ReturnType<typeof getHiddenConversationsFactory>;
    getLabels: ReturnType<typeof getLabelsFactory>;
    getListBoard: ReturnType<typeof getListBoardFactory>;
    getListReminder: ReturnType<typeof getListReminderFactory>;
    getMute: ReturnType<typeof getMuteFactory>;
    getOwnId: ReturnType<typeof getOwnIdFactory>;
    getPinConversations: ReturnType<typeof getPinConversationsFactory>;
    getPollDetail: ReturnType<typeof getPollDetailFactory>;
    getQR: ReturnType<typeof getQRFactory>;
    getQuickMessage: ReturnType<typeof getQuickMessageFactory>;
    getReceivedFriendRequests: ReturnType<typeof getReceivedFriendRequestsFactory>;
    getReminder: ReturnType<typeof getReminderFactory>;
    getReminderResponses: ReturnType<typeof getReminderResponsesFactory>;
    getSentFriendRequest: ReturnType<typeof getSentFriendRequestFactory>;
    getStickers: ReturnType<typeof getStickersFactory>;
    getStickersDetail: ReturnType<typeof getStickersDetailFactory>;
    getUnreadMark: ReturnType<typeof getUnreadMarkFactory>;
    getUserInfo: ReturnType<typeof getUserInfoFactory>;
    inviteUserToGroups: ReturnType<typeof inviteUserToGroupsFactory>;
    joinGroup: ReturnType<typeof joinGroupFactory>;
    keepAlive: ReturnType<typeof keepAliveFactory>;
    lastOnline: ReturnType<typeof lastOnlineFactory>;
    leaveGroup: ReturnType<typeof leaveGroupFactory>;
    lockPoll: ReturnType<typeof lockPollFactory>;
    parseLink: ReturnType<typeof parseLinkFactory>;
    removeFriend: ReturnType<typeof removeFriendFactory>;
    removeFriendAlias: ReturnType<typeof removeFriendAliasFactory>;
    removeGroupDeputy: ReturnType<typeof removeGroupDeputyFactory>;
    removeQuickMessage: ReturnType<typeof removeQuickMessageFactory>;
    removeReminder: ReturnType<typeof removeReminderFactory>;
    removeUnreadMark: ReturnType<typeof removeUnreadMarkFactory>;
    removeUserFromGroup: ReturnType<typeof removeUserFromGroupFactory>;
    resetHiddenConversPin: ReturnType<typeof resetHiddenConversPinFactory>;
    reuseAvatar: ReturnType<typeof reuseAvatarFactory>;
    sendCard: ReturnType<typeof sendCardFactory>;
    sendDeliveredEvent: ReturnType<typeof sendDeliveredEventFactory>;
    sendFriendRequest: ReturnType<typeof sendFriendRequestFactory>;
    sendLink: ReturnType<typeof sendLinkFactory>;
    sendMessage: ReturnType<typeof sendMessageFactory>;
    sendReport: ReturnType<typeof sendReportFactory>;
    sendSeenEvent: ReturnType<typeof sendSeenEventFactory>;
    sendSticker: ReturnType<typeof sendStickerFactory>;
    sendTypingEvent: ReturnType<typeof sendTypingEventFactory>;
    sendVideo: ReturnType<typeof sendVideoFactory>;
    sendVoice: ReturnType<typeof sendVoiceFactory>;
    setHiddenConversations: ReturnType<typeof setHiddenConversationsFactory>;
    setMute: ReturnType<typeof setMuteFactory>;
    setPinnedConversations: ReturnType<typeof setPinnedConversationsFactory>;
    unblockUser: ReturnType<typeof unblockUserFactory>;
    undo: ReturnType<typeof undoFactory>;
    undoFriendRequest: ReturnType<typeof undoFriendRequestFactory>;
    updateAutoDeleteChat: ReturnType<typeof updateAutoDeleteChatFactory>;
    updateGroupSettings: ReturnType<typeof updateGroupSettingsFactory>;
    updateHiddenConversPin: ReturnType<typeof updateHiddenConversPinFactory>;
    updateLabels: ReturnType<typeof updateLabelsFactory>;
    updateLang: ReturnType<typeof updateLangFactory>;
    updateProfile: ReturnType<typeof updateProfileFactory>;
    updateQuickMessage: ReturnType<typeof updateQuickMessageFactory>;
    updateSettings: ReturnType<typeof updateSettingsFactory>;
    uploadAttachment: ReturnType<typeof uploadAttachmentFactory>;
    custom: ReturnType<typeof customFactory>;
    constructor(ctx: ContextSession, zpwServiceMap: ZPWServiceMap, wsUrls: string[]);
}
